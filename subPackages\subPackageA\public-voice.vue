<template>
	<view class="page-container">
		<!-- 顶部筛选tabs -->
		<view class="filter-tabs-container">
			<scroll-view :scroll-x="true" class="tabs-scroll" :scroll-with-animation="true">
				<view class="tabs-wrapper">
					<view
						v-for="(tab, index) in scenarioTabs"
						:key="index"
						@click="selectScenario(tab.value, index)"
						class="tab-item"
						:class="selectedScenarioIndex === index ? 'tab-active' : 'tab-normal'"
					>
						{{ tab.label }}
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 音色列表 -->
		<view class="voice-list-container">
			<view class="voice-grid">
				<view
					v-for="(voice, index) in filteredVoiceList"
					:key="index"
					@click="selectVoice(voice)"
					class="voice-item"
					:class="selectedVoice && selectedVoice.code === voice.code ? 'voice-selected' : ''"
				>
					<!-- 音色图片 -->
					<view class="voice-image-container">
						<image
							class="voice-image"
							:src="imgUrl + '238.png'"
							mode="aspectFit"
						></image>
						<!-- 选中状态图标 -->
						<image
							v-if="selectedVoice && selectedVoice.code === voice.code"
							class="selected-icon"
							:src="imgUrl + '241.png'"
						></image>
					</view>

					<!-- 音色名称 -->
					<view class="voice-name">{{ voice.resource_display }}</view>

					<!-- 试听按钮 -->
					<view
						@click.stop="playDemo(voice, index)"
						class="play-button"
						:class="voice.isPlaying ? 'playing' : ''"
					>
						<image
							class="play-icon"
							:src="voice.isPlaying ? imgUrl + '242.png' : imgUrl + '240.png'"
						></image>
					</view>
				</view>
			</view>

			<!-- 空状态 -->
			<view v-if="filteredVoiceList.length === 0" class="empty-state">
				<view class="empty-text">暂无该类型的音色</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				imgUrl: this.$imgUrl,
				// 原始音色数据
				voicePackList: [],
				// 筛选tabs数据
				scenarioTabs: [],
				// 当前选中的筛选类型
				selectedScenario: 'all',
				selectedScenarioIndex: 0,
				// 过滤后的音色列表
				filteredVoiceList: [],
				// 当前选中的音色
				selectedVoice: null,
				// 音频播放上下文
				audioContext: null,
				// 加载状态
				isLoading: false,
			}
		},
		computed: {
			// 根据选中的筛选类型过滤音色列表
			computedFilteredList() {
				if (this.selectedScenario === 'all') {
					return this.voicePackList;
				}
				return this.voicePackList.filter(voice =>
					voice.details && voice.details.recommended_scenario === this.selectedScenario
				);
			}
		},
		onLoad() {
			this.getVoicePackList();
		},
		onUnload() {
			// 页面卸载时清理音频资源
			this.cleanupAudio();
		},
		methods: {
			// 获取公共音色列表数据
			async getVoicePackList() {
				try {
					this.isLoading = true;
					const result = await this.$http.get({
						url: this.$api.voicePackList
					});

					if (result && Array.isArray(result)) {
						// 为每个音色添加播放状态
						this.voicePackList = result.map(voice => ({
							...voice,
							isPlaying: false
						}));

						// 生成筛选tabs
						this.generateScenarioTabs();

						// 初始化过滤列表
						this.updateFilteredList();
					} else {
						this.$sun.toast('音色数据格式错误', 'none');
					}
				} catch (error) {
					console.error('获取音色列表失败:', error);
					this.$sun.toast('获取音色列表失败', 'none');
				} finally {
					this.isLoading = false;
				}
			},

			// 生成筛选tabs（去重recommended_scenario）
			generateScenarioTabs() {
				const scenarios = new Set();

				// 提取所有的recommended_scenario并去重
				this.voicePackList.forEach(voice => {
					if (voice.details && voice.details.recommended_scenario) {
						scenarios.add(voice.details.recommended_scenario);
					}
				});

				// 构建tabs数据，添加"全部"选项
				this.scenarioTabs = [
					{ label: '全部', value: 'all' },
					...Array.from(scenarios).map(scenario => ({
						label: scenario,
						value: scenario
					}))
				];
			},

			// 选择筛选类型
			selectScenario(scenario, index) {
				this.selectedScenario = scenario;
				this.selectedScenarioIndex = index;
				this.updateFilteredList();
			},

			// 更新过滤后的列表
			updateFilteredList() {
				this.filteredVoiceList = this.computedFilteredList;
			},

			// 选择音色
			selectVoice(voice) {
				this.selectedVoice = voice;
				// 可以在这里触发事件，将选中的voice_type传递给父组件
				this.$emit('voiceSelected', {
					voice_type: voice.details.voice_type,
					voice_data: voice
				});
			},

			// 播放试听音频
			async playDemo(voice, index) {
				if (!voice.details || !voice.details.demo_link) {
					this.$sun.toast('该音色暂无试听音频', 'none');
					return;
				}

				try {
					// 停止当前播放的音频
					this.stopCurrentAudio();

					// 创建新的音频上下文
					this.audioContext = uni.createInnerAudioContext();
					this.audioContext.src = voice.details.demo_link;

					// 设置播放状态
					this.setVoicePlayingState(index, true);

					// 音频播放事件监听
					this.audioContext.onPlay(() => {
						console.log('开始播放:', voice.resource_display);
					});

					this.audioContext.onEnded(() => {
						console.log('播放结束:', voice.resource_display);
						this.setVoicePlayingState(index, false);
						this.cleanupAudio();
					});

					this.audioContext.onError((error) => {
						console.error('音频播放错误:', error);
						this.$sun.toast('音频播放失败', 'none');
						this.setVoicePlayingState(index, false);
						this.cleanupAudio();
					});

					// 开始播放
					this.audioContext.play();

				} catch (error) {
					console.error('播放音频时出错:', error);
					this.$sun.toast('播放失败', 'none');
					this.setVoicePlayingState(index, false);
				}
			},

			// 停止当前播放的音频
			stopCurrentAudio() {
				if (this.audioContext) {
					this.audioContext.stop();
					this.audioContext.destroy();
					this.audioContext = null;
				}

				// 重置所有音色的播放状态
				this.voicePackList.forEach(voice => {
					voice.isPlaying = false;
				});
				this.updateFilteredList();
			},

			// 设置音色播放状态
			setVoicePlayingState(index, isPlaying) {
				if (this.filteredVoiceList[index]) {
					this.$set(this.filteredVoiceList[index], 'isPlaying', isPlaying);

					// 同时更新原始数据中的状态
					const originalIndex = this.voicePackList.findIndex(voice =>
						voice.code === this.filteredVoiceList[index].code
					);
					if (originalIndex !== -1) {
						this.$set(this.voicePackList[originalIndex], 'isPlaying', isPlaying);
					}
				}
			},

			// 清理音频资源
			cleanupAudio() {
				if (this.audioContext) {
					this.audioContext.destroy();
					this.audioContext = null;
				}
			},
		}
	}
</script>

<style scoped lang="scss">
	.page-container {
		background-color: #1B1B1B;
		min-height: 100vh;
		padding-bottom: 40rpx;
	}

	/* 筛选tabs样式 */
	.filter-tabs-container {
		background-color: #1B1B1B;
		padding: 20rpx 0;
		border-bottom: 1px solid #333333;
	}

	.tabs-scroll {
		width: 100%;
		white-space: nowrap;
	}

	.tabs-wrapper {
		display: flex;
		padding: 0 20rpx;
	}

	.tab-item {
		flex-shrink: 0;
		padding: 16rpx 32rpx;
		margin-right: 20rpx;
		border-radius: 20rpx;
		font-size: 28rpx;
		text-align: center;
		transition: all 0.3s ease;
	}

	.tab-normal {
		background-color: #333333;
		color: #999999;
	}

	.tab-active {
		background-color: #4BA2FF;
		color: #FFFFFF;
	}

	/* 音色列表样式 */
	.voice-list-container {
		padding: 20rpx;
	}

	.voice-grid {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
	}

	.voice-item {
		width: 220rpx;
		margin-bottom: 30rpx;
		background-color: #333333;
		border-radius: 16rpx;
		padding: 20rpx;
		position: relative;
		transition: all 0.3s ease;
	}

	.voice-selected {
		background-color: #4BA2FF;
		transform: scale(1.05);
	}

	.voice-image-container {
		position: relative;
		display: flex;
		justify-content: center;
		margin-bottom: 16rpx;
	}

	.voice-image {
		width: 120rpx;
		height: 120rpx;
		border-radius: 12rpx;
	}

	.selected-icon {
		position: absolute;
		top: -8rpx;
		right: -8rpx;
		width: 40rpx;
		height: 40rpx;
	}

	.voice-name {
		color: #FFFFFF;
		font-size: 26rpx;
		text-align: center;
		margin-bottom: 16rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.play-button {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 60rpx;
		background-color: rgba(255, 255, 255, 0.1);
		border-radius: 30rpx;
		transition: all 0.3s ease;
	}

	.play-button.playing {
		background-color: #4BA2FF;
	}

	.play-icon {
		width: 32rpx;
		height: 32rpx;
	}

	/* 空状态样式 */
	.empty-state {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 400rpx;
	}

	.empty-text {
		color: #999999;
		font-size: 28rpx;
	}

	/* 通用样式 */
	.display-a {
		display: flex;
		align-items: center;
	}

	.display-a-jc {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.display-a-js {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
</style>
