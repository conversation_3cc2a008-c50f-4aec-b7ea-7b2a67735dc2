<template>
	<view>
		<view class="h_20rpx"></view>
		<view class="margin_20rpx">

			<view class="font-weight_bold font-size_36rpx margin-bottom_20rpx color_FFFFFF">克隆标题<span
					class="color_FF0000 margin-left_10rpx">*</span></view>
			<input type="text" v-model="name" class="c-input" placeholder="请输入声音克隆标题" placeholder-class="placeholder" />

			<!-- <view class="font-weight_bold font-size_36rpx margin-bottom_20rpx color_FFFFFF">参考音频</view> -->

			<view class="copywriting sel-copy" v-if="copywriting">
				<view class="font-size_30rpx text-align_center margin-bottom_20rpx">参考文本</view>
				<view class="color_FFFFFF margin-bottom_30rpx">{{copywriting}}</view>
				<view class="display-a-jc random margin-left-auto" @click="getRandExample()">
					<image class="img-214" :src="imgUrl+'214.png'"></image>
					<view class="font-size_26rpx">随机</view>
				</view>
			</view>

			<view class="font-weight_bold font-size_36rpx margin-bottom_20rpx color_FFFFFF">参考音频</view>

			<block v-if="audioUrl">
				<view class="display-a margin-bottom_30rpx">
					<image class="img-218" :src="imgUrl+'218.png'"></image>
					<view class="color_FFFFFF">{{audioName}}</view>
				</view>
				<view class="display-ac-jc">
					<image class="img-play" @click="playAudio()"
						:src="isPlay == 1 ? imgUrl+'sound-recording/pause2.png' : imgUrl+'sound-recording/play2.png'">
					</image>
					<view class="color_999999 text-align_center margin-bottom_20rpx">-------- 或者 --------</view>
					<view class="display-a margin-bottom_60rpx">
						<view class="display-a-jc" @click="getIsAudioUrl()">
							<image class="img-215" :src="imgUrl+'217.png'"></image>
							<view class="color_2DFF9A" style="color: #4196FF;">录制音频</view>
						</view>
						<view style="width: 80rpx;"></view>
						<view class="display-a-jc" @click="chooseFile()">
							<image class="img-215" :src="imgUrl+'216.png'"></image>
							<view class="color_2DFF9A">重新上传音频</view>
						</view>
					</view>
				</view>
			</block>
			<block v-else>
				<view style="margin: 20rpx 0 20rpx;" v-if="isOpen == 2">
					<sound-recording @confirm="getConfirm"></sound-recording>
				</view>

				<view class="color_999999 text-align_center margin-bottom_20rpx">-------- 或者 --------</view>

				<view class="display-a-jc margin-bottom_60rpx" @click="getBatch(2)">
					<image class="img-215" :src="imgUrl+'215.png'"></image>
					<view class="color_2DFF9A">从微信聊天记录里上传</view>
				</view>
			</block>


		</view>

		<view style="height: 210rpx;"></view>

		<view class="pos-bott">
			<view class="display-a-jc margin-bottom_20rpx" v-if="cloneSet.protocol_name">
				<image @click="getIsProtocol()" class="img-219" :src="isProtocol ? imgUrl+'220.png' : imgUrl+'219.png'">
				</image>
				<view @click="openProtocol()" class="color_CBCACA">同意并且确认<span
						class="color_4BA2FF">《{{cloneSet.protocol_name}}》</span></view>
			</view>

			<view class="v-but" @click="vBut()">
				开始克隆
				<span class="margin-left_10rpx" v-if="isMember != 1">
					{{tallySetObj.voice_deduct}}点/1次
				</span>
			</view>
		</view>

		<!-- <block v-if="customerConfig.customer_type == 'on_line'">
			<button open-type="contact">
				<image class="kefu" @click="getKefu()" :class="isKefu ? 'kefu-2' : 'kefu-1'" :src="imgUrl+'kefu.gif'" ></image>
			</button>
		</block>
		<block v-else>
			<image class="kefu" @click="getKefu()" :class="isKefu ? 'kefu-2' : 'kefu-1'" :src="imgUrl+'kefu.gif'" ></image>
		</block> -->




		<view class="h_20rpx"></view>

		<sunui-popup ref="pop">
			<template v-slot:content>
				<view style="overflow:auto;padding:10rpx 30rpx 20rpx;">
					<scroll-view :scroll-y="true" style="height: 700rpx;">
						<rich-parser :html="cloneSet.protocol "
							domain="https://6874-html-foe72-1259071903.tcb.qcloud.la" lazy-load ref="article" selectable
							show-with-animation use-anchor>
							<!-- 加载中... -->
						</rich-parser>
					</scroll-view>

					<view class="display-a-js margin-top_20rpx ">
						<view class="c-close" @click="close(1)">关闭</view>
						<view class="c-agree" @click="vBut()">同意并克隆</view>
					</view>

				</view>
			</template>
		</sunui-popup>

		<sunui-popup ref="pop5">
			<template v-slot:content>
				<image class="img-qr-code" :src="customerConfig.customer_qr_code"></image>
			</template>
		</sunui-popup>

		<sunui-popup ref="pop3">
			<template v-slot:content>
				<view class="pop-bg" :style="{'background-image': 'url('+imgUrl+'224.png'+')'}">
					<image @click="closeBatch(1)" class="img-233" :src="imgUrl+'233.png'"></image>
					<view class="p-title">{{isVoice == 1 ? '录音要求' : '上传音频要求'}}</view>
					<view class="margin-bottom_20rpx">
						<image class="img-221" mode="widthFix" :src="imgUrl+'221.png'"></image>
					</view>
					<view class="display-a margin-bottom_20rpx">
						<view class="c-tips"></view>
						<view class="font-size_26rpx color_FFFFFF">支持在线录音或上传音频，音频要mp3(小写)格式</view>
					</view>
					<view class="display-a margin-bottom_20rpx">
						<view class="c-tips"></view>
						<view class="font-size_26rpx color_FFFFFF">上传的音频长度15秒以上，容量3M以内</view>
					</view>
					<view class="display-a margin-bottom_20rpx">
						<view class="c-tips"></view>
						<view class="font-size_26rpx color_FFFFFF">录制过程中要保证环境安静不得有明显噪音</view>
					</view>
					<view class="display-a margin-bottom_20rpx">
						<view class="c-tips"></view>
						<view class="font-size_26rpx color_FFFFFF">声音不要进行混响和任何特效处理要保持自然</view>
					</view>
					<view class="display-a margin-bottom_20rpx">
						<view class="c-tips"></view>
						<view class="font-size_26rpx color_FFFFFF">录制过程中只能有一个人的声音禁止多人人声</view>
					</view>
					<view class="display-a margin-bottom_20rpx">
						<view class="c-tips"></view>
						<view class="font-size_26rpx color_FFFFFF">录制时要语速均衡，声音大小不要忽大忽小</view>
					</view>

					<view class="display-a-js" style="margin-top: 160rpx;">
						<view class="c-agree" style="width: 690rpx;" @click="chooseFile()">
							{{isVoice == 1 ? '继续录音' : '上传mp3文件'}}
						</view>
					</view>
				</view>
			</template>
		</sunui-popup>


	</view>
</template>

<script>
	const voiceInnerAudioContext = uni.createInnerAudioContext();
	export default {
		data() {
			return {

				imgUrl: this.$imgUrl,

				isSex: '1', //1男 2女

				time: '00:00', //录音时长

				audioUrl: '', //音频路径
				audioName: '', //音频名称

				name: '', //名称

				isPlay: '2', //1播放 2暂停

				isLimit: '1', //弹出录音要求 2直接录音

				isOpen: '2', //1打开 2关闭

				isVoice: '', //1录音 2上传

				isProtocol: false, //

				isWhether: true, //判断重复点击

				isMember: uni.getStorageSync("isMember"), //1开启会员

				tallySetObj: uni.getStorageSync('tallySetObj'), //扣点设置

				system: uni.getStorageSync('system'),

				copywriting: '', //随机文案

				cloneSet: {},

				isKefu: true, //true隐藏 false展开

				customerConfig: uni.getStorageSync('customerConfig'), //客服配置
			}
		},



		onLoad() {
			this.getCloneSet();
			this.getRandExample();
		},

		onShow() {

		},

		onUnload() {

			if (voiceInnerAudioContext && (!voiceInnerAudioContext.paused)) {
				voiceInnerAudioContext.stop();
			}

		},

		methods: {

			getKefu() {
				if (this.isKefu) {
					this.isKefu = false;
				} else {
					if (this.customerConfig.customer_type == 'on_line') {
						return;
					} else if (this.customerConfig.customer_type == 'phone') {
						if (this.customerConfig.customer_phone) {
							this.$sun.phone(this.customerConfig.customer_phone);
						} else {
							this.$sun.toast("暂无联系方式", 'error');
						}
					} else if (this.customerConfig.customer_type == 'qr_code') {
						this.$refs.pop5.show({
							style: 'background-color:#000;width:600rpx;border-radius:10rpx;',
							bottomClose: true,
							shadeClose: false,
						});
					}
				}
			},

			getIsAudioUrl() {
				this.audioUrl = '';
			},

			getBatch(type) {
				this.isOpen = 1;
				this.isVoice = type;
				this.$refs.pop3.show({
					style: 'background-color:#222127;width:750rpx;border-radius: 10rpx 10rpx 0 0;',
					anim: 'bottom',
					position: 'bottom',
					shadeClose: false,
					rgba: 'rgba(50,50,50,.6)'
				});
			},
			closeBatch(type) {
				this.$refs.pop3.close();
				this.isOpen = 2;
			},

			//协议
			getIsProtocol() {
				this.isProtocol = !this.isProtocol;
			},
			//打开协议
			openProtocol() {
				this.isOpen = 1;
				this.isProtocol = true;

				this.$refs.pop.show({
					title: this.cloneSet.protocol_name,
					style: 'background-color:#fff;width:700rpx;border-radius:10rpx;',
					// bottomClose: true,
					shadeClose: false,
				});

			},

			close() {
				this.isOpen = 2;
				this.$refs.pop.close();
			},

			//随机文案
			async getRandExample() {
				const result = await this.$http.post({
					url: this.$api.randExample
				});
				if (result.errno == 0) {
					this.copywriting = result.data;
				}
			},

			//克隆设置
			async getCloneSet() {
				const result = await this.$http.post({
					url: this.$api.cloneSet
				});
				if (result.errno == 0) {
					this.cloneSet = result.data;
				}
			},

			//录音上传
			getConfirm(e) {
				// console.log("录音文件==>",e);
				this.upload(e);
			},

			//删除音频
			delAudio() {
				uni.showModal({
					title: '提示',
					content: '确认删除 ' + this.audioName + ' 吗?',
					success: async (res) => {
						if (res.confirm) {
							voiceInnerAudioContext.stop();
							this.audioUrl = ''; //音频路径
							this.audioName = ''; //音频名称
						} else if (res.cancel) {
							// console.log('用户点击取消');
						}
					}
				});
			},

			//播放音频
			playAudio() {

				voiceInnerAudioContext.src = this.audioUrl;

				if (this.isPlay == 2) {
					this.isPlay = 1;
					voiceInnerAudioContext.play();
					voiceInnerAudioContext.onPlay(() => {
						// console.log('开始播放');
					});
					voiceInnerAudioContext.onEnded(() => {
						this.isPlay = 2;
						voiceInnerAudioContext.destroy();
						// this.$sun.toast("音频播放完成");
						// console.log('音频播放结束：');
					});
					voiceInnerAudioContext.onError((err) => {
						voiceInnerAudioContext.destroy();
						// console.log('播放音频出错：', err);
					});
				} else {
					this.isPlay = 2;
					voiceInnerAudioContext.pause();
					voiceInnerAudioContext.onPause(() => {
						// console.log('暂停播放');
					});
				}

				// console.log("---->",this.isPlay);

			},


			//克隆训练
			async vBut() {

				if (uni.getStorageSync('uid')) {

					if (!this.name) {
						this.$sun.toast("请输入标题", 'error');
						return;
					}

					if (!this.audioUrl) {
						this.$sun.toast("请先上传音频", 'error');
						return;
					}

					if (!this.isProtocol) {
						this.$sun.toast("请先勾选协议", 'error');
						return;
					}

					if (!this.isWhether) {
						return;
					}
					this.isWhether = false;

					this.close();

					const result = await this.$http.post({
						url: this.$api.voiceTraining,
						data: {
							uid: uni.getStorageSync('uid'),
							name: this.name,
							voice_urls: [this.audioUrl]
						}
					});
					if (result.errno == 0) {
						this.$sun.toast(result.message);
						setTimeout(() => {
							uni.navigateTo({
								url: '/pages/assets/digital-assets?tabsId=3&tabsNextId=1'
							})
							this.isWhether = true;
						}, 2000);
					} else {
						if (result.message == 'voice name already exists') {
							this.$sun.toast("声音名称已存在", 'none');
						} else {
							if (result.errno == -2) {
								uni.showModal({
									content: result.message,
									cancelText: "取消",
									confirmText: "去开通",
									success: (res) => {
										if (res.confirm) {
											uni.navigateTo({
												url: '/pages/my/member'
											})
										} else if (res.cancel) {

										}
									}
								})
							} else {
								this.$sun.toast(result.message, 'none');
							}
						}
						this.isWhether = true;
					}

				} else {
					uni.showModal({
						content: "请先登录",
						cancelText: "取消",
						confirmText: "去登录",
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pages/auth/auth?type=1'
								})
							} else if (res.cancel) {
								// this.navig();
							}
						}
					})
				}

			},

			navig() {
				let pages = getCurrentPages(); //获取所有页面栈实例列表

				if (pages.length == 1) {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				} else {
					uni.navigateBack();
				}
			},

			//克隆记录
			getRecord() {

				if (uni.getStorageSync('uid')) {
					// uni.navigateTo({
					// 	url: '/pages/index/voice/record?type=1'
					// })
					uni.navigateTo({
						url: '/pages/assets/digital-assets?tabsId=3&tabsNextId=1'
					})
				} else {
					uni.showModal({
						content: "请先登录",
						cancelText: "取消",
						confirmText: "去登录",
						success: (res) => {
							if (res.confirm) {
								uni.navigateTo({
									url: '/pages/auth/auth?type=1'
								})
							} else if (res.cancel) {
								// this.navig();
							}
						}
					})
				}
			},

			getSex(type) {
				this.isSex = type;
			},

			//音频上传
			chooseFile() {

				if (this.isVoice == 1) {
					this.isLimit = 2;
					this.closeBatch(2);
				} else {
					uni.chooseMessageFile({
						count: 1,
						type: 'file',
						// extension: ['.mp3', '.m4a', '.wav', 'wav', 'mp3', 'm4a', '.MP3', '.M4A', '.WAV', 'WAV',
						// 	'MP3',
						// 	'M4A'
						// ],
						extension: ['.mp3', 'mp3', '.MP3'],
						success: (res) => {
							console.log("从聊天记录中获取文件", res);
							const {
								errMsg,
								tempFiles
							} = res;
							if (errMsg == "chooseMessageFile:ok" && tempFiles.length) {
								const {
									name,
									size,
									path
								} = tempFiles[0];
								console.log("临时文件", {
									size,
									path
								});
								// if ((name.slice(-4) != ".mp3") && (name.slice(-4) != ".MP3") && (name.slice(-
								// 		4) !=
								// 		".m4a") && (name.slice(-4) != ".M4A") && (name.slice(-4) !=
								// 		".wav") && (name.slice(-4) != ".WAV")) {
								// 	return uni.showToast({
								// 		icon: "none",
								// 		title: "请上传mp3,m4a,wav格式音频文件！",
								// 		duration: 2000,
								// 	});
								// }
								if ((name.slice(-4) != ".mp3")) {
									return uni.showToast({
										icon: "none",
										title: "请上传mp3格式音频文件！",
										duration: 2000,
									});
								}
								if (size / 1024 / 1024 > 3) {
									return uni.showToast({
										icon: "none",
										title: "请上传3M内的音频文件！",
									});
								}

								console.log("从聊天记录中获取文件", name, path);
								// formData.value.audioName = name
								this.upload(path);
							}
						},
						fail: (err) => {
							console.log("失败原因：", err)
						},
					});
				}

			},
			upload(path) {
				uni.showLoading()
				// 调用uni.uploadFile将文件上传至服务器
				uni.uploadFile({
					url: this.$api.upload, // 设置上传接口地址
					filePath: path, // 需要上传的文件路径
					name: 'file', // 后台接收文件时对应的字段名称
					fileType: 'audio', // 指定文件类型
					header: {
						// "Content-Type": "multipart/form-data",
					},
					success: (response) => {

						console.log("response----->", response);

						// TODO: 处理上传成功后的操作
						const data = JSON.parse(response.data)
						// console.log('文件上传成功',data);
						// formData.value.accompanyInfo = data.data.url
						if (data.errno != 0) {
							uni.showToast({
								title: '文件上传失败',
								icon: 'none'
							});
						} else {
							this.audioUrl = data.data;
							let index = this.audioUrl.lastIndexOf('/'); // 获取最后一个/的位置
							let lastSegment = this.audioUrl.substring(index + 1); // 截取最后一个/后的值
							this.audioName = lastSegment;
							this.$sun.toast("文件上传成功");
							this.closeBatch(1);
						}
						uni.hideLoading()
						return
					},
					fail(error) {
						console.log("error---------->", error);
						uni.hideLoading()
						// console.log('文件上传失败');
						// console.log(error);

						// TODO: 处理上传失败后的操作
					}
				});
			},

		}
	}
</script>

<style lang="scss">
	.img-qr-code {
		width: 500rpx;
		height: 500rpx;
		margin: 50rpx;
	}

	.kefu-2 {
		right: -50rpx;
		opacity: 0.5;
		transition: all 1s linear;
	}

	.kefu-1 {
		right: 10rpx;
		transition: all 1s linear;
	}

	.kefu {
		width: 100rpx;
		height: 100rpx;
		position: fixed;
		z-index: 99;
		// right: -50rpx;
		bottom: 240rpx;
	}

	.img-233 {
		width: 34rpx;
		height: 34rpx;
		position: absolute;
		z-index: 1;
		top: 42rpx;
		right: 30rpx;
	}

	.pop-bg {
		position: relative;
		width: 750rpx;
		// height: 912rpx;
		height: 960rpx;
		background-repeat: no-repeat;
		background-size: contain;
		padding: 34rpx 30rpx;
		color: #FFF;
	}

	.pos-bott {
		position: fixed;
		bottom: 30rpx;
		z-index: 9;
	}

	.img-play {
		width: 160rpx;
		height: 160rpx;
		margin-bottom: 20rpx;
	}

	.img-218 {
		width: 34rpx;
		height: 34rpx;
		margin-right: 8rpx;
	}

	.img-221 {
		width: 690rpx;
		// height: 192rpx;
	}

	.p-title {
		background: linear-gradient(102.95deg, rgb(29, 130, 255), rgb(226, 79, 250));
		-webkit-background-clip:
			text;
		-webkit-text-fill-color:
			transparent;
		background-clip:
			text;
		text-fill-color:
			transparent;
		font-weight: 600;
		letter-spacing: 4%;
		font-size: 40rpx;
		text-align: center;
		margin-bottom: 40rpx;
	}

	.color_4BA2FF {
		color: #4BA2FF;
		font-size: 26rpx;
	}

	.color_CBCACA {
		color: #cbcaca;
		font-size: 26rpx;
	}

	.img-219 {
		width: 34rpx;
		height: 34rpx;
		margin-right: 12rpx;
	}

	.color_2DFF9A {
		color: #2Dff9a;
		font-size: 32rpx;
		font-weight: bold;
	}

	.img-215 {
		width: 34rpx;
		height: 34rpx;
		margin-right: 12rpx;
	}

	.img-214 {
		width: 34rpx;
		height: 34rpx;
		margin-right: 10rpx;
	}

	.random {
		width: 130rpx;
		border-radius: 100rpx;
		background: linear-gradient(90.00deg, rgb(75, 162, 255), rgb(109, 10, 179) 100%);
		padding: 8rpx 0;
		color: #FFF;
	}

	.c-agree {
		width: 350rpx;
		text-align: center;
		border-radius: 10rpx;
		background: linear-gradient(90.00deg, rgb(80, 203, 250), rgb(66, 58, 254) 97.869%);
		color: #FFF;
		font-size: 32rpx;
		padding: 20rpx 0;
	}

	.c-close {
		width: 270rpx;
		text-align: center;
		border: 1px solid rgb(203, 202, 202);
		border-radius: 10rpx;
		background: rgb(255, 255, 255);
		font-size: 32rpx;
		color: #929292;
		padding: 20rpx 0;
	}

	.img-169 {
		width: 96rpx;
		height: 96rpx;
		margin-bottom: 12rpx;
	}

	.sel-copy {
		border: 1px solid rgb(65, 150, 255);
		border-radius: 10rpx;
		color: #4196FF;
		margin-bottom: 20rpx;
	}

	.copywriting {
		width: 710rpx;
		background-color: #1F1F1F;
		border-radius: 10rpx;
		padding: 24rpx 28rpx;
	}

	.c-tips {
		background: linear-gradient(180.00deg, rgb(161, 91, 246), rgb(131, 32, 253) 100%);
		width: 16rpx;
		height: 16rpx;
		border-radius: 50%;
		margin-right: 12rpx;
	}

	.img-58 {
		width: 26rpx;
		height: 26rpx;
		margin-left: 6rpx;
		margin-top: 2rpx;
	}

	.img-63 {
		width: 32rpx;
		height: 32rpx;
		margin-right: 8rpx;
	}

	.clone-line {
		width: 8rpx;
		height: 28rpx;
		background: linear-gradient(180.00deg, rgb(187, 119, 248), rgb(135, 80, 242) 100%);
		border-radius: 2rpx;
		margin-right: 10rpx;
	}

	.img-164 {
		width: 34rpx;
		height: 34rpx;
		margin-bottom: 14rpx;
	}

	.img-163 {
		width: 40rpx;
		height: 40rpx;
		margin-bottom: 14rpx;
	}

	.c-input {
		width: 670rpx;
		// background-color: #434343;
		background-color: #2A2A2A;
		border-radius: 10rpx;
		padding: 20rpx;
		// margin: 20rpx 0;
		margin-bottom: 40rpx;
		color: #FFF;
	}

	.sex-line2 {
		background-color: #111317;
		width: 46rpx;
		height: 4rpx;
		border-radius: 10rpx;
	}

	.sex-line {
		background-color: #423AFE;
		width: 46rpx;
		height: 4rpx;
		border-radius: 10rpx;
	}

	.v-name {
		width: 400rpx;
		color: #FFF;
		font-size: 30rpx;
	}

	.img-96 {
		width: 34rpx;
		height: 34rpx;
		margin-left: 20rpx;
	}

	.v-play {
		width: 110rpx;
		border-radius: 100rpx;
		background-color: #8C54F2;
		padding: 6rpx 0;
		color: #FFF;
		font-size: 24rpx;
		margin-left: auto;
	}

	.img-94 {
		width: 22rpx;
		height: 22rpx;
		margin-right: 4rpx;
	}

	.img-93 {
		width: 48rpx;
		height: 48rpx;
		margin-right: 16rpx;
	}

	.v-audio {
		width: 610rpx;
		height: 200rpx;
		margin: 40rpx 70rpx 0;
		background-color: #323232;
	}

	.color_9F9F9F {
		color: #9F9F9F;
		font-size: 24rpx;
	}

	.padd_20rpx {
		padding: 30rpx 0;
		border-bottom: 1px solid rgb(57, 57, 57);
	}

	.img-65 {
		width: 40rpx;
		height: 40rpx;
	}

	.p-but {
		width: 710rpx;
		border-radius: 20rpx;
		background: linear-gradient(90.00deg, rgb(80, 203, 250), rgb(66, 58, 254) 97.869%);
		padding: 24rpx 0;
		text-align: center;
		color: #FFF;
		font-size: 32rpx;
		margin: 70rpx 0 20rpx;
	}

	.v-but {
		// position: fixed;
		// bottom: 40rpx;
		width: 710rpx;
		border-radius: 20rpx;
		background: linear-gradient(90.00deg, rgb(80, 203, 250), rgb(66, 58, 254) 97.869%);
		padding: 26rpx 0;
		text-align: center;
		color: #FFF;
		font-size: 32rpx;
		margin: 0 20rpx 20rpx;
	}

	.color_B7B7B7 {
		color: #B7B7B7;
		font-size: 24rpx;
	}

	.v-add {
		color: #9D61F5;
		font-size: 30rpx;
	}

	.v-upload {
		width: 710rpx;
		text-align: center;
		padding: 40rpx 0 30rpx;
	}

	.img-21 {
		width: 20rpx;
		height: 20rpx;
		margin-right: 20rpx;
	}

	.p-bo {
		border-bottom: 1px solid rgb(96, 96, 96);
	}

	.v-input {
		padding: 20rpx;
		width: 670rpx;
		color: #FFF;
	}

	.placeholder {
		color: #C8C8C8;
	}

	.v-top {
		background-color: #323232;
		width: 710rpx;
		border-radius: 10rpx;
		margin: 20rpx 20rpx 30rpx;
	}

	.v-tips {
		font-size: 26rpx;
		text-align: center;
		margin-bottom: 50rpx;
		color: #8E8E8E;
	}

	.sex-2 {
		font-weight: 600;
		background: linear-gradient(180.00deg, rgb(79, 195, 250), rgb(66, 58, 254));
		-webkit-background-clip:
			text;
		-webkit-text-fill-color:
			transparent;
		background-clip:
			text;
		text-fill-color:
			transparent;
	}

	.sex-1 {
		color: #818181;
	}

	.sex {
		width: 375rpx;
		text-align: center;
		font-size: 32rpx;
	}

	.img-70 {
		width: 112rpx;
		height: 112rpx;
		margin: 0 60rpx;
	}

	.v-frame {
		width: 710rpx;
		border: 1px dashed rgb(106, 106, 106);
		border-radius: 10rpx;
		margin-bottom: 30rpx;
		padding: 60rpx 0;
	}

	.img-69 {
		width: 32rpx;
		height: 32rpx;
		margin-right: 6rpx;
	}

	.img-72 {
		width: 558rpx;
		height: 10rpx;
		margin-left: 96rpx;
		margin-bottom: 52rpx;
	}

	page {
		border-top: none;
		background-color: #111317;
	}
</style>